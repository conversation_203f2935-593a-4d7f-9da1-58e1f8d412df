using OfficeOpenXml;
using ProjectECN.Models;
using ProjectECN.DTO;
using System.Globalization;

namespace ProjectECN.Services;

public class ECNExcelProcessingService
{
    private readonly ILogger<ECNExcelProcessingService> _logger;

    public ECNExcelProcessingService(ILogger<ECNExcelProcessingService> logger)
    {
        _logger = logger;
        // Set EPPlus license context for EPPlus 8+
        // For now, we'll handle the license exception in the code
    }

    /// <summary>
    /// Process all Excel files in the ECN folder structure and convert to RawEcndatum entities
    /// </summary>
    public async Task<ExcelProcessingResult> ProcessECNExcelFilesAsync(string extractFolderPath, 
        List<PSMCEcnDto> psmcEcns, string currentUserId, IProgress<ExcelProcessingProgress>? progress = null)
    {
        var result = new ExcelProcessingResult();
        var allEcnData = new List<RawEcndatum>();

        try
        {
            _logger.LogInformation($"Starting Excel processing in folder: {extractFolderPath}");

            // Find all Excel files in the ECN folder structure
            var excelFiles = Directory.GetFiles(extractFolderPath, "*.xlsx", SearchOption.AllDirectories)
                .Where(f => !Path.GetFileName(f).StartsWith("~")) // Exclude temporary files
                .ToList();

            _logger.LogInformation($"Found {excelFiles.Count} Excel files to process");

            if (!excelFiles.Any())
            {
                result.Success = true;
                result.Message = "No Excel files found to process";
                return result;
            }

            var processedFiles = 0;
            foreach (var excelFile in excelFiles)
            {
                try
                {
                    progress?.Report(new ExcelProcessingProgress
                    {
                        Stage = "Processing Excel Files",
                        ProcessedFiles = processedFiles,
                        TotalFiles = excelFiles.Count,
                        CurrentFile = Path.GetFileName(excelFile)
                    });

                    var fileData = await ProcessSingleExcelFileAsync(excelFile, psmcEcns, currentUserId);
                    allEcnData.AddRange(fileData);
                    
                    processedFiles++;
                    _logger.LogInformation($"Processed Excel file: {Path.GetFileName(excelFile)} - {fileData.Count} records");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error processing Excel file: {excelFile}");
                    result.ErrorMessages.Add($"Error processing {Path.GetFileName(excelFile)}: {ex.Message}");
                }
            }

            result.Success = true;
            result.ProcessedFiles = processedFiles;
            result.TotalFiles = excelFiles.Count;
            result.EcnData = allEcnData;
            result.Message = $"Successfully processed {processedFiles} Excel files with {allEcnData.Count} total records";

            _logger.LogInformation($"Excel processing completed. Processed {processedFiles}/{excelFiles.Count} files, {allEcnData.Count} total records");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Excel processing");
            result.Success = false;
            result.ErrorMessage = $"Excel processing failed: {ex.Message}";
        }

        return result;
    }

    /// <summary>
    /// Process a single Excel file and extract ECN data
    /// </summary>
    private async Task<List<RawEcndatum>> ProcessSingleExcelFileAsync(string excelFilePath, 
        List<PSMCEcnDto> psmcEcns, string currentUserId)
    {
        var ecnDataList = new List<RawEcndatum>();

        await Task.Run(() =>
        {
            try
            {
                // Set license if not already set
                if (ExcelPackage.LicenseContext == LicenseContext.Commercial)
                {
                    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                }
            }
            catch
            {
                // License context might already be set
            }

            using var package = new ExcelPackage(new FileInfo(excelFilePath));
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();

            if (worksheet == null)
            {
                _logger.LogWarning($"No worksheet found in Excel file: {excelFilePath}");
                return;
            }

            var fileName = Path.GetFileName(excelFilePath);
            
            // Extract header information from first 4 rows
            var ccCode = GetCellValue(worksheet, 1, 2); // B1
            var prodType = GetCellValue(worksheet, 2, 2); // B2  
            var bomGname = GetCellValue(worksheet, 3, 2); // B3
            var createDateStr = GetCellValue(worksheet, 4, 2); // B4

            // Parse create date
            DateTime createdDate = DateTime.MinValue;
            if (!string.IsNullOrEmpty(createDateStr))
            {
                if (!DateTime.TryParseExact(createDateStr, "yyyy/MM/dd", null, DateTimeStyles.None, out createdDate))
                {
                    if (!DateTime.TryParse(createDateStr, out createdDate))
                    {
                        _logger.LogWarning($"Could not parse create date '{createDateStr}' in file {fileName}");
                        createdDate = DateTime.MinValue;
                    }
                }
            }

            _logger.LogInformation($"Processing Excel file: {fileName} - CC Code: {ccCode}, Prod Type: {prodType}, BOM: {bomGname}, Date: {createDateStr}");

            // Process tabular data starting from row 5
            var currentRow = 5;
            var maxRows = worksheet.Dimension?.End.Row ?? 0;

            while (currentRow <= maxRows)
            {
                var ecnNo = GetCellValue(worksheet, currentRow, 1)?.Trim(); // Column A
                
                // Skip empty rows
                if (string.IsNullOrEmpty(ecnNo))
                {
                    currentRow++;
                    continue;
                }

                // Determine if this ECN belongs to PSMC based on the processed PSMC data
                var belongsToPsmc = psmcEcns.Any(p => p.EcnNumber == ecnNo && p.QualifiesForPSMC);

                var ecnData = new RawEcndatum
                {
                    Id = Guid.NewGuid(),
                    FileName = fileName,
                    CreatedDate = createdDate,
                    EcnNo = ecnNo,
                    CcCode = ccCode ?? string.Empty,
                    ProdType = prodType ?? string.Empty,
                    BomGname = bomGname ?? string.Empty,
                    Page = GetCellValue(worksheet, currentRow, 2)?.Trim() ?? string.Empty, // Column B
                    Line = GetCellValue(worksheet, currentRow, 3)?.Trim() ?? string.Empty, // Column C
                    Block = GetCellValue(worksheet, currentRow, 4)?.Trim() ?? string.Empty, // Column D
                    OldPartNumber = GetCellValue(worksheet, currentRow, 5)?.Trim() ?? string.Empty, // Column E
                    NewPartNumber = GetCellValue(worksheet, currentRow, 6)?.Trim() ?? string.Empty, // Column F
                    PartName = GetCellValue(worksheet, currentRow, 7)?.Trim() ?? string.Empty, // Column G
                    DwgNumber = GetCellValue(worksheet, currentRow, 8)?.Trim() ?? string.Empty, // Column H
                    DwgEcnNo = GetCellValue(worksheet, currentRow, 9)?.Trim() ?? string.Empty, // Column I
                    BelongsToPsmc = belongsToPsmc,
                    RecordCreatedDate = DateTime.Now,
                    RecordCreatedBy = currentUserId
                };

                ecnDataList.Add(ecnData);
                currentRow++;
            }
        });

        return ecnDataList;
    }

    /// <summary>
    /// Get cell value as string, handling different data types
    /// </summary>
    private string? GetCellValue(ExcelWorksheet worksheet, int row, int column)
    {
        try
        {
            var cellValue = worksheet.Cells[row, column].Value;
            return cellValue?.ToString()?.Trim();
        }
        catch (Exception ex)
        {
            _logger.LogWarning($"Error reading cell [{row},{column}]: {ex.Message}");
            return null;
        }
    }
}

public class ExcelProcessingResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public List<string> ErrorMessages { get; set; } = new();
    public int ProcessedFiles { get; set; }
    public int TotalFiles { get; set; }
    public List<RawEcndatum> EcnData { get; set; } = new();
}

public class ExcelProcessingProgress
{
    public string Stage { get; set; } = string.Empty;
    public int ProcessedFiles { get; set; }
    public int TotalFiles { get; set; }
    public string CurrentFile { get; set; } = string.Empty;
    public int Percentage => TotalFiles > 0 ? ProcessedFiles * 100 / TotalFiles : 0;
}
